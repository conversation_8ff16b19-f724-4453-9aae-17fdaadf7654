# TinyVoice iOS App

This is the iOS version of the TinyVoice audio chat application, recreated from the web frontend using SwiftUI.

## Features

- **Continuous Audio Recording**: Records audio continuously with a 15-second rolling buffer
- **Manual Recording**: Send the last 15 seconds of audio to the AI with a button press
- **Wake Word Detection**: Listen for "Scarlett" wake word via WebSocket connection to backend
- **Execution Keyword**: Say "skynet" to force immediate processing during wake word sessions
- **Real-time Transcription**: Shows both user and AI transcripts
- **Audio Playback**: Plays AI responses with proper audio session management
- **Audio Effects**: Typing sounds during processing, tune for wake word detection, punch sound for execution keyword

## Architecture

The app is built with SwiftUI and follows MVVM architecture with three main managers:

### AudioManager
- Handles continuous audio recording using AVAudioEngine
- Manages 15-second rolling buffer
- Converts audio to WAV format
- Handles wake word detection notifications
- Manages audio effects (typing sounds)

### WebSocketManager
- Connects to backend wake word detection service
- Sends audio data for real-time wake word processing
- Handles wake word and execution keyword detection
- Manages connection state with automatic reconnection

### APIManager
- Sends audio files to `/api/chat` endpoint
- <PERSON><PERSON> multipart form data uploads
- Processes JSON responses with audio and transcripts
- Manages audio response playback

## Requirements

- iOS 18.5+ or macOS 15.3+
- Microphone permissions
- Network access to localhost:8000 (backend server)
- Backend server running with wake word detection enabled

## Setup

1. Open `TinyVoice.xcodeproj` in Xcode
2. Ensure the backend server is running on `localhost:8000`
3. Build and run the app
4. Grant microphone permissions when prompted
5. Use "Start Listening" to enable wake word detection
6. Use "Send Last 15 Seconds" for manual recording

## Usage

### Manual Recording
1. The app continuously records audio in a 15-second buffer
2. Click "Send Last 15 Seconds" to send the current buffer to the AI
3. Wait for the response and transcript to appear

### Wake Word Detection
1. Click "Start Listening" to enable wake word detection
2. Say "Scarlett" to trigger wake word detection
3. Continue speaking your request
4. Either wait for 2 seconds of silence or say "skynet" to process
5. The app will automatically return to listening for the wake word

## Audio Files

The app includes three audio files that should be added to the Xcode project:
- `Typing.wav` - Played during API processing
- `tune.wav` - Played when wake word is detected
- `punch.mp3` - Played when execution keyword is detected

## Permissions

The app requires:
- Microphone access (`NSMicrophoneUsageDescription`)
- Network client access (for API and WebSocket connections)
- App Sandbox with microphone and network entitlements

## Error Handling

- Automatic WebSocket reconnection with exponential backoff
- Comprehensive error messages for network and audio issues
- Graceful handling of permission denials
- Proper cleanup of audio resources

## Backend Compatibility

This iOS app is fully compatible with the existing Python backend and maintains the same API contracts as the web frontend.
